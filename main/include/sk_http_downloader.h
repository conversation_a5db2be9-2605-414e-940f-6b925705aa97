/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_http_downloader.h
 * @description: HTTP文件下载到PSRAM模块
 * @author: <PERSON>
 * @date: 2025-01-20
 */
#ifndef SK_HTTP_DOWNLOADER_H
#define SK_HTTP_DOWNLOADER_H

#include <stdint.h>
#include <stdbool.h>
#include "sk_common.h"
#include "freertos/FreeRTOS.h"
#include "freertos/ringbuf.h"

// 前向声明状态机相关类型
struct SkSmItem;
struct SkSubStateInfo;
struct SkSmEvent;
typedef void* SkStateHandler;
typedef void (*SkSmStateEndCallback)(SkStateHandler handler, int32_t state);

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    RingbufHandle_t ringbuf;
    uint8_t *storage;
    size_t totalSize;
} SkHttpDownloadData;

typedef struct {
    uint8_t version;        // OPUS版本
    uint8_t channels;       // 声道数
    uint16_t preSkip;       // 预跳过样本数
    uint32_t sampleRate;    // 采样率
    uint16_t outputGain;    // 输出增益
} SkOpusHeader;

typedef struct {
    uint8_t *data;          // OPUS包数据
    size_t size;            // 包大小
} SkOpusPacket;



/**
 * @brief 下载文件到PSRAM环形缓冲区
 * @param url 文件URL
 * @param downloadData 输出下载数据结构
 * @return SK_RET_SUCCESS 成功，其他值失败
 */
sk_err_t SkHttpDownloadFile(const char *url, SkHttpDownloadData *downloadData);

/**
 * @brief 释放下载资源
 * @param downloadData 下载数据结构
 */
void SkHttpDownloadFree(SkHttpDownloadData *downloadData);

/**
 * @brief 从环形缓冲区解析OPUS头部信息
 * @param ringbuf 环形缓冲区句柄
 * @param header 输出OPUS头部信息
 * @return SK_RET_SUCCESS 成功，其他值失败
 */
sk_err_t SkOggParseOpusHeader(RingbufHandle_t ringbuf, SkOpusHeader *header);

/**
 * @brief 从环形缓冲区提取下一个OPUS音频包
 * @param ringbuf 环形缓冲区句柄
 * @param packet 输出OPUS包
 * @return SK_RET_SUCCESS 成功，SK_RET_NOT_FOUND 没有更多包，其他值失败
 */
sk_err_t SkOggGetNextOpusPacket(RingbufHandle_t ringbuf, SkOpusPacket *packet);

/**
 * @brief 重置OGG解析状态（用于重新开始解析）
 */
void SkOggResetParser(void);

/**
 * @brief 释放OPUS包资源
 * @param packet OPUS包
 */
void SkOggFreeOpusPacket(SkOpusPacket *packet);



/**
 * @brief 重置OGG解析位置（不释放数据，用于重新开始解析）
 */
void SkOggResetParserPosition(void);

/**
 * @brief 队列集成OPUS流式播放（利用现有音频队列系统）
 * @param downloadData 下载的数据
 * @return SK_RET_SUCCESS 成功，其他值失败
 */
sk_err_t SkOpusQueueStreamPlay(SkHttpDownloadData *downloadData);

// 状态机接口
int32_t SkSmHttpAudioInit(struct SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler);

// HTTP音频任务控制接口
sk_err_t SkHttpAudioStartOggParsing(void);
sk_err_t SkHttpAudioStartOpusQueue(void);

#ifdef __cplusplus
}
#endif

#endif // SK_HTTP_DOWNLOADER_H
